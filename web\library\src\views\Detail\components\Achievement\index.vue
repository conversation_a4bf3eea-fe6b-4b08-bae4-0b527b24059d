<template>
  <div class="cpt-achievement">
    <div class="comp-title achievement-title">
      成就进度
      <div class="sub-title">
        <span style="margin-right: 2px"
          >{{ getTotalAchievedCount() }}/{{ getTotalAchievementCount() }}</span
        ><span
          >({{ getAchievementPercentage() }}%)</span
        >
      </div>
    </div>
    <div class="sub-comp-block-list">
      <!-- 稀有成就组 -->
      <AchievementListItem
        v-if="achievementData.rare_list && achievementData.rare_list.length > 0"
        type="gold"
        title="稀有"
        subtitle="(完成率 < 10%)"
        :list="achievementData.rare_list"
        :show-background-image="true"
        @click-group="handleClickAchievementGroup"
        @click-item="handleClickAchievementItem"
      />

      <!-- 普通成就组 -->
      <AchievementListItem
        v-if="achievementData.ordinary_list && achievementData.ordinary_list.length > 0"
        type="silver"
        title="普通"
        subtitle="(完成率 10-100%)"
        :list="achievementData.ordinary_list"
        :show-background-image="false"
        @click-group="handleClickAchievementGroup"
        @click-item="handleClickAchievementItem"
      />
    </div>

    <!-- 最近成就 -->
    <div
      class="achievement-list"
      v-if="achievementData.recent_list && achievementData.recent_list.length > 0"
    >
      <div
        class="achievement-item"
        v-for="(item, index) in displayedRecentList"
        :key="index"
        @click="handleOpenAchievementDetail(item)"
        v-tooltip="getRecentTooltipConfig(item)"
      >
        <div class="achievement-img">
          <div class="img-cover"></div>
          <img
            :class="item.finishLoad ? 'fade' : 'none'"
            :src="item.icon"
          />
          <div
            class="gold-ring"
            v-if="item.ach_level == 3"
          ></div>
        </div>
        <div class="info">
          <div class="name-time">
            <div class="name">
              {{ item.name }}
            </div>
            <div class="time">
              {{ formatAchievedTime(item.achieved_time, item.achieved) }}
            </div>
          </div>
          <div class="desc">
            {{ item.desc }}
          </div>
        </div>
        <div class="progress-block">
          <div class="cup">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
            >
              <rect
                width="14"
                height="14"
                fill="url(#pattern0_492_3313)"
              />
              <defs>
                <pattern
                  id="pattern0_492_3313"
                  patternContentUnits="objectBoundingBox"
                  width="1"
                  height="1"
                >
                  <use
                    xlink:href="#image0_492_3313"
                    transform="scale(0.0138889)"
                  />
                </pattern>
                <image
                  id="image0_492_3313"
                  width="72"
                  height="72"
                  preserveAspectRatio="none"
                  xlink:href="data:image/png;base64,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"
                />
              </defs>
            </svg>

            <div class="percent">{{ item.achieved_percent }}%</div>
          </div>
          <div class="progress-rail">
            <div
              class="progress-inner"
              :style="{ width: item.achieved_percent + '%' }"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="no-data"
      v-else
    >
      暂无成就解锁
    </div>
    <CheckMore
      v-if="achievementData.recent_list && achievementData.recent_list.length > 5"
      :isExpanded="isExpanded"
      @toggle="handleToggle"
    />
  </div>
</template>

<script setup name="Achievement">
import { defineProps, ref, computed } from 'vue';
import CheckMore from '@/components/func/CheckMore.vue';
import AchievementListItem from './components/AchievementListItem.vue';
import { formatAchievedTime } from '@heybox-app-web-shared/utils';

const props = defineProps({
  achievementData: {
    type: Object,
  },
});

const isExpanded = ref(false);

const displayedRecentList = computed(() => {
  if (!props.achievementData.recent_list) return [];

  if (isExpanded.value) {
    return props.achievementData.recent_list;
  } else {
    return props.achievementData.recent_list.slice(0, 5);
  }
});

const handleToggle = () => {
  isExpanded.value = !isExpanded.value;
};

const getRareAchievedCount = () => {
  if (!props.achievementData.rare_list) return 0;
  return props.achievementData.rare_list.filter(item => item.achieved === true).length;
};

const getOrdinaryAchievedCount = () => {
  if (!props.achievementData.ordinary_list) return 0;
  return props.achievementData.ordinary_list.filter(item => item.achieved === true).length;
};

const getTotalAchievedCount = () => {
  return getRareAchievedCount() + getOrdinaryAchievedCount();
};

const getTotalAchievementCount = () => {
  const rareCount = props.achievementData.rare_list ? props.achievementData.rare_list.length : 0;
  const ordinaryCount = props.achievementData.ordinary_list ? props.achievementData.ordinary_list.length : 0;
  return rareCount + ordinaryCount;
};

const getAchievementPercentage = () => {
  const total = getTotalAchievementCount();
  if (total === 0) return '0.0';
  const achieved = getTotalAchievedCount();
  return ((achieved / total) * 100).toFixed(1);
};


const handleClickAchievementGroup = (type) => {
  console.log('Achievement group clicked:', type);
};

const handleClickAchievementItem = (item) => {
  console.log(item);
};

const handleOpenAchievementDetail = (item) => {
  console.log(item);
};

const getRecentTooltipConfig = (item) => {
  if (!item || !item.desc || item.desc.trim() === '') return { disable: true };

  return {
    placement: 'top',
    popperClass: 'recent-achievement-tooltip',
    text: `<div style="max-width: 244px; padding: 8px; font-size: 12px; color: #14191e; line-height: 16px;">${item.desc}</div>`,
    pointer: 'center'
  };
};
</script>

<style lang="scss">
.cpt-achievement {
  display: flex;
  padding: 14px 16px 0px 16px;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  border-radius: 8px;
  background: var(---general-color-primary-0, #fff);

  .achievement-title {
    display: flex;
    align-items: center;
    gap: 4px;

    .sub-title {
      color: var(---general-color-text-3, #8c9196);
      font-size: 12px;
      line-height: 16px;
    }
  }

  .sub-comp-block-list {
    .sub-comp-block {
      .sub-comp-block-title {
        .sub-title {
          font-size: 12px;
          line-height: 16px;
          color: var(---, #fff);
        }
      }
    }
  }

  .gold,
  .silver,
  .bronze {
    position: relative;
    min-width: 173px;
    padding: 8px 12px 10px 10px;
    border-radius: 2px;
    .desc {
      position: relative;
      top: 1px;
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      .text {
        white-space: nowrap;
        font-weight: 500;
        font-size: 10px;
        line-height: 14px;
        color: #ffffff;
      }
    }
    .sub-comp-list {
      position: relative;
      z-index: 2;
      display: flex;
      gap: 4px;
      overflow: hidden;

      &.expanded {
        flex-wrap: wrap;
      }

      .more-count {
        display: flex;
        padding: 8px 5px;
        justify-content: center;
        align-items: center;
        width: 34px;
        height: 34px;
        border-radius: $general-size-radius-3;
        background: rgba(0, 0, 0, 0.20);
        cursor: pointer;

        color: #FFF;
        text-align: center;
        font-family: "Helvetica Neue";
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 18px;
      }
    }
    .cups {
      position: absolute;
      z-index: 1;
      right: 0;
      top: 0;
      width: 44px;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  .gold {
    background: linear-gradient(86.35deg, #fbb63f 8.29%, #ffde6a 98.24%);
    .cpt-achievement-item {
      position: relative;
      overflow: hidden;
      background: rgba(200, 205, 210, 0.6);
      .gold-ring {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 90px;
        height: 90px;
        background: linear-gradient(
          139.56deg,
          #edc23f 23.5%,
          #fff615 37.74%,
          #fafbbd 43.4%,
          #d1a300 64.09%,
          #f1c644 85.79%
        );
        animation: rotateBg 3s infinite linear;
      }
      &.grey {
        opacity: 0.5;
        background: rgba(200, 205, 210, 0.6);
        img {
          filter: grayscale(100%);
        }
      }
    }
  }
  .silver,
  .bronze {
    background: linear-gradient(86.35deg, #aecfff 8.29%, #c4daed 98.24%);
    .cpt-achievement-item {
      background: rgba(200, 205, 210, 0.6);
      &.grey {
        opacity: 0.5;
        img {
          filter: grayscale(100%);
        }
      }
    }
  }
  .bronze {
    margin-right: 0;
    background: linear-gradient(81.24deg, #f79d49 2.25%, #f2a760 105.33%);
  }
  .achievement-list {
    width: 100%;
    overflow: hidden;
    .achievement-item {
      width: 100%;
      position: relative;
      display: flex;
      align-items: center;
      overflow: hidden;
      padding: 6px 0;
      .achievement-img {
        flex-shrink: 0;
        position: relative;
        z-index: 2;
        width: 34px;
        height: 34px;
        padding: 2px;
        margin-right: 10px;
        overflow: hidden;
        background: rgba(200, 205, 210, 0.6);
        border-radius: 2px;
        .img-cover {
          position: absolute;
          z-index: 3;
          top: 2px;
          left: 2px;
          width: calc(100% - 4px);
          height: calc(100% - 4px);
          border-radius: 3px;
          background: gray;
        }
        img {
          position: relative;
          z-index: 4;
          width: 100%;
          height: 100%;
          border-radius: 2px;
          object-fit: cover;
        }
        .gold-ring {
          position: absolute;
          left: 0;
          top: 0;
          z-index: 1;
          width: 90px;
          height: 90px;
          background: linear-gradient(
            139.56deg,
            #edc23f 23.5%,
            #fff615 37.74%,
            #fafbbd 43.4%,
            #d1a300 64.09%,
            #f1c644 85.79%
          );
          animation: rotateBg 3s infinite linear;
        }
      }
      .info {
        flex: 1;
        overflow: hidden;
        margin-right: 6px;
        .name-time {
          display: flex;
          align-items: center;
          margin-bottom: 2px;
          .name {
            max-width: 50%;
            margin-right: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 500;
            font-size: 12px;
            line-height: 17px;
            color: #14191e;
          }
          .time {
            position: relative;
            top: -1px;
            font-family: Helvetica Neue;
            font-size: 10px;
            line-height: 11px;
            color: #c8cdd2;
          }
        }
        .desc {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 10px;
          line-height: 14px;
          color: #8c9196;
        }
      }
      .progress-block {
        flex-shrink: 0;
        position: relative;
        top: -2px;
        width: 57px;
        .cup {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          img {
            width: 14px;
            height: 14px;
          }
          .percent {
            position: relative;
            left: -2px;
            margin-left: auto;
            font-family: Helvetica Neue;
            font-size: 12px;
            line-height: 14px;
            text-align: right;
            color: #14191e;
          }
        }
        .progress-rail {
          position: relative;
          width: 100%;
          height: 4px;
          background: #f3f4f5;
          border-radius: 4px;
          .progress-inner {
            position: absolute;
            left: 0;
            top: 0;
            min-width: 4px;
            height: 100%;
            border-radius: 4px;
            background: #0f7c10;
          }
        }
      }
      &:not(:last-child) {
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 56px;
          width: calc(100% - 56px);
          height: 1px;
          background: #f3f4f5;
          transform: scaleY(0.5);
        }
      }
    }
  }
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
    padding-bottom: 28px;
    font-size: 12px;
    line-height: 17px;
    color: #c8cdd2;
  }
}

:global(.recent-achievement-tooltip) {
  .tooltip {
    max-width: 322px !important;
    width: max-content !important;
  }
  .tooltip-text {
    white-space: normal !important;
    word-wrap: break-word;
  }
}
</style>
