<template>
  <div
    v-if="show && !hiddenEllipsis"
    :style="tooltipStyle"
    class="cpt-tooltip"
  >
    <div
      class="tooltip"
      ref="tooltip"
      :class="[
        popperClass,
        'placement-' + placement,
        pointer !== 'center' ? `pointer-${placement}-${pointer}` : '',
        {'no-ellipsis': autoEllipsis}
      ]"
      :style="calPos"
    >
      <div
        v-if="tooltipText"
        ref="tooltip-text"
        class="tooltip-text"
        v-html="tooltipText"
        ></div>
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'


// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: true
  },
  placement: {
    type: String,
    default: 'top' // 默认值为 'top'
  },
  pointer: {
    type: String,
    default: 'center' // 默认值为 'center'
  }, 
  targetRect: {
    type: Object
  },
  popperClass: {
    type: String,
    default: ''
  },
  text: {
    type: String,
    default: ''
  },
  autoEllipsis: {
    type: Boolean,
    default: false
  },
  target_el: {
    default: null
  }
});

// Refs
const hiddenEllipsis = ref(false);
const tooltipText = ref(props.text);
const elWidth = ref(0);
const tooltip = ref(null);
const tooltipTextRef = ref(null);

// Computed
const calPos = computed(() => {      
  if (props.placement == 'top') {
    return {
      bottom: `11px`,
    }
  } else if (props.placement == 'bottom') {
    return {
      top: `calc(100% + 11px + ${props.targetRect.height}px)`,
    }
  } else if (props.placement == 'left') {
    return {
      right: `calc(11px + ${props.targetRect.width/2}px)`,
      top: `calc( 100% + ${props.targetRect.height/2}px)`,
    }
  } else {
    return {
      left: `calc(11px + ${props.targetRect.width/2}px)`,
      top: `calc( 100% + ${props.targetRect.height/2}px)`,
    }
  }
});

const tooltipStyle = computed(() => {
  const windowWidth = window.innerWidth;
  const targetCenterX = props.targetRect.x + props.targetRect.width/2;

  const tooltipRightEdge = targetCenterX + elWidth.value/2;

  // 是否超出屏幕右侧
  const rightOverflow = tooltipRightEdge - windowWidth - 20;
  const isRightOutOfScreen = rightOverflow > 0;

  if (isRightOutOfScreen) {
    let leftPosition;
    let arrowOffset;

    if (isRightOutOfScreen) {
      leftPosition = targetCenterX - rightOverflow;
      arrowOffset = rightOverflow;
    }

    return {
      left: `${leftPosition}px`,
      top: `${props.targetRect.y}px`,
      '--arrow-offset': `${arrowOffset}px`,
    }
  }
  return {
    left: `${targetCenterX}px`,
    top: `${props.targetRect.y}px`,
    '--arrow-offset': '0px',
  }
});

// Methods
const updateText = (text) => {
  tooltipText.value = text
};

const checkAutoEllipsis = () => {
  if (props.autoEllipsis && props.target_el) {
    nextTick(() => {
      const hasHorizontalOverflow = props.target_el.scrollWidth > props.target_el.clientWidth;
      const hasVerticalOverflow = props.target_el.scrollHeight > props.target_el.clientHeight;
      hiddenEllipsis.value = !hasHorizontalOverflow && !hasVerticalOverflow;
    })
  }
};

// Lifecycle
onMounted(() => {
  nextTick(() => {
    if (tooltip.value) {
      elWidth.value = tooltip.value.offsetWidth
    }
    checkAutoEllipsis()
  })
});

// Expose methods for parent component
defineExpose({
  updateText,
  checkAutoEllipsis
});
</script>

<style lang="scss">
.cpt-tooltip { 
  position: fixed;
  display: inline-block;
  z-index: 9999;
  .tooltip { 
    text-align: center;
    padding: 6px 8px 8px 8px;
    border-radius: 5px;
    opacity: 1;
    transition: opacity 1s;
    position: absolute;
    z-index: 1;
    background: rgba(250, 251, 252, 0.99);
    width: max-content;
    max-width: 228px; // 所有tooltip最大宽度为228px
    text-align: left;
    box-shadow: 0 3px 15px 0 rgba(0, 0, 0, 0.35);
    & > * {
      font-size: 14px;
      font-weight: 400;
      color: $general-color-text-2;
    }
    &::after {
      content: "";
      position: fixed;
      border-width: 5px;
      border-style: solid;
      border-color: rgba(250, 251, 252, 0.99) transparent transparent transparent;
    }
    &.placement-top {
      left: 50%; 
      transform: translateX(-50%);
      &::after {
        top: 100%;
        left: calc(50% + var(--arrow-offset, 0px));
        margin-left: -5px;
      }
    }
    &.placement-bottom {
      left: 50%;
      transform: translateX(-50%);
      &::after {
        bottom: 100%;
        left: 50%;
        margin-left: -5px;
        border-color: transparent transparent rgba(250, 251, 252, 0.99) transparent;
      }
    }
    &.placement-left {
      transform: translateY(-50%);
      &::after {
        left: 100%;
        top: 50%;
        margin-top: -5px;
        border-color: transparent transparent transparent rgba(250, 251, 252, 0.99);
      }
    }
    &.placement-right {
      transform: translateY(-50%);
      &::after {
        right: 100%;
        top: 50%;
        margin-top: -5px;
        border-color: transparent rgba(250, 251, 252, 0.99) transparent transparent;
      }
    }
    &.pointer-top-right {
      left: 50%; 
      transform: translateX(-70%);
      &::after {
        top: 100%;
        left: calc(70% + var(--arrow-offset, 0px));
      }
    }
    &.pointer-top-left {
      left: 50%; 
      transform: translateX(-30%);
      &::after {
        top: 100%;
        left: 30%;
      }
    }
    &.pointer-bottom-left {
      left: 50%;
      transform: translateX(-30%);
      &::after {
        bottom: 100%;
        left: 30%;
      }
    }
    &.pointer-bottom-right {
      left: 50%;
      transform: translateX(-70%);
      &::after {
        bottom: 100%;
        left: 70%;
      }
    }

    &.pointer-left-top {
      transform: translateY(-70%);
      &::after {
        left: 100%;
        top: 70%;
      }
    }
    &.pointer-left-bottom {
      transform: translateY(-30%);
      &::after {
        left: 100%;
        top: 30%;
      }
    }
    &.pointer-right-top {
      transform: translateY(-70%);
      &::after {
        right: 100%;
        top: 70%;
      }
    }
    &.pointer-right-bottom {
      transform: translateY(-30%);
      &::after {
        right: 100%;
        top: 30%;
      }
    }
    &.plus-tooltip {
      max-width: 316px;
    }
    &.white {
      background: #FFFFFF;
      border: 1px solid $general-color-stroke-1;
      box-shadow: 0px 2px 13px rgba(0, 0, 0, 0.07);
      border-radius: 5px;
      font-weight: 400;
      text-align: center;
      & > * {
        font-size: 14px;
        font-weight: 400;
        color: $general-color-text-5;
      }
      &::after {
        border-top-color: $general-color-bg-4;
      }
      &.placement-bottom {
        &::after {
          border-bottom-color: $general-color-bg-4;
        }
      }
      &.placement-left {
        &::after {
          border-left-color: $general-color-bg-4;
        }
      }
      &.placement-right {
        &::after {
          border-right-color: $general-color-bg-4;
        }
      }
    }
  }
  .share-item__tooltip {
    transform: translateY(9px) translateX(-50%);
    color: $general-color-text-2 !important;
    border: 1px solid $general-color-stroke-1;
    padding: 7px 8px;
  }
}
</style>